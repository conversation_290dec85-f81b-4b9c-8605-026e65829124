name: Deploy to <PERSON>Picker Website
on:
  workflow_dispatch:  # Allows manual triggering
  push:
    branches:
      - main
      - new-multi  # You can replace this with your branch name

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install SSH Key
      uses: shimataro/ssh-key-action@v2
      with:
        key: ${{ secrets.SERVER_SSH_KEY }}
        known_hosts: ${{ secrets.KNOWN_HOSTS }}

    - name: Deploy website files to host
      env:
        SERVER_IP: ${{ secrets.SERVER_IP }}
        SERVER_USER: ${{ secrets.SERVER_USER }}
        DEPLOY_DIR: ${{ secrets.DEPLOY_DIR }}
      run: |
        echo "Deploying website files to host..."
        # Deploy all files except git-related and node_modules
        rsync -avz --delete \
          --exclude='.git/' \
          --exclude='.gitignore' \
          --exclude='.github/' \
          --exclude='node_modules/' \
          --exclude='multiplayer/server/node_modules/' \
          ./ $SERVER_USER@$SERVER_IP:$DEPLOY_DIR

        if [ $? -eq 0 ]; then
          echo "✅ Website files deployed successfully"
        else
          echo "❌ Failed to deploy website files"
          exit 1
        fi

    - name: Setup Node.js and PM2
      env:
        SERVER_IP: ${{ secrets.SERVER_IP }}
        SERVER_USER: ${{ secrets.SERVER_USER }}
      run: |
        echo "Setting up Node.js and PM2..."

        # Install Node.js 18.x if not already installed
        ssh -o ConnectTimeout=30 -o ServerAliveInterval=60 $SERVER_USER@$SERVER_IP "
          if ! command -v node &> /dev/null || [ \$(node -v | cut -d. -f1 | tr -d 'v') -lt 18 ]; then
            echo 'Installing Node.js 18.x...'
            curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
            sudo apt-get install -y nodejs
          else
            echo 'Node.js is already installed:' \$(node -v)
          fi
        "

        # Install PM2 globally if not already installed
        ssh -o ConnectTimeout=30 -o ServerAliveInterval=60 $SERVER_USER@$SERVER_IP "
          if ! command -v pm2 &> /dev/null; then
            echo 'PM2 not found, installing globally...'

            # Clean up any corrupted PM2 installation first
            echo 'Cleaning up any existing PM2 installation...'
            sudo rm -rf /usr/local/lib/node_modules/pm2 2>/dev/null || true
            sudo rm -rf /usr/local/bin/pm2 2>/dev/null || true
            sudo rm -rf ~/.pm2 2>/dev/null || true

            # Clear npm cache to avoid conflicts
            echo 'Clearing npm cache...'
            npm cache clean --force 2>/dev/null || sudo npm cache clean --force 2>/dev/null || true

            # Install PM2 without sudo
            echo 'Installing PM2 without sudo...'
            if npm install -g pm2; then
              echo 'PM2 installed successfully'
            else
              echo 'PM2 installation failed'
              exit 1
            fi

            # Verification step: Ensure PM2 is properly installed and accessible
            echo 'Verifying PM2 installation...'
            if command -v pm2 &> /dev/null; then
              PM2_VERSION=\$(pm2 -v)
              echo 'PM2 installation verified successfully. Version:' \$PM2_VERSION
            else
              echo 'PM2 installation verification failed - command not found'
              echo 'Checking if PM2 exists in common locations...'
              ls -la /usr/local/bin/pm2 2>/dev/null || echo 'PM2 not found in /usr/local/bin/'
              ls -la /usr/bin/pm2 2>/dev/null || echo 'PM2 not found in /usr/bin/'
              exit 1
            fi
          else
            PM2_VERSION=\$(pm2 -v)
            echo 'PM2 is already installed. Version:' \$PM2_VERSION
          fi
        "

        echo "✅ Node.js and PM2 setup completed"

    - name: Install multiplayer server dependencies
      env:
        SERVER_IP: ${{ secrets.SERVER_IP }}
        SERVER_USER: ${{ secrets.SERVER_USER }}
        DEPLOY_DIR: ${{ secrets.DEPLOY_DIR }}
      run: |
        echo "Installing multiplayer server dependencies..."

        # Install dependencies for the multiplayer server
        ssh $SERVER_USER@$SERVER_IP "cd $DEPLOY_DIR/multiplayer/server && \
          echo 'Installing multiplayer server dependencies...' && \
          npm ci || npm install"

        if [ $? -eq 0 ]; then
          echo "✅ Multiplayer server dependencies installed successfully"
        else
          echo "❌ Failed to install multiplayer server dependencies"
          exit 1
        fi

    - name: Configure and start server with PM2
      env:
        SERVER_IP: ${{ secrets.SERVER_IP }}
        SERVER_USER: ${{ secrets.SERVER_USER }}
        DEPLOY_DIR: ${{ secrets.DEPLOY_DIR }}
      run: |
        echo "Configuring and starting server with PM2..."

        # Configure and start the server with PM2
        ssh $SERVER_USER@$SERVER_IP "cd $DEPLOY_DIR && \
          echo 'Stopping existing PM2 process if it exists...' && \
          pm2 delete personpicker 2>/dev/null || true && \
          pm2 flush && \
          echo 'Starting the server with PM2...' && \
          NODE_ENV=production PORT=4000 pm2 start multiplayer/server/server.js --name personpicker --max-memory-restart 300M && \
          echo 'Saving PM2 process list...' && \
          pm2 save && \
          echo 'Configuring PM2 to start on system boot...' && \
          pm2 startup | grep -v PM2 | grep -v 'undefined' | tail -n 1 | sh || true"

        if [ $? -eq 0 ]; then
          echo "✅ Server configured and started with PM2 successfully"
        else
          echo "❌ Failed to configure and start server with PM2"
          exit 1
        fi

    - name: Verify server is running
      env:
        SERVER_IP: ${{ secrets.SERVER_IP }}
        SERVER_USER: ${{ secrets.SERVER_USER }}
      run: |
        echo "Verifying server is running..."

        # Wait for the server to start
        echo "Waiting for server to start..."
        sleep 10

        # Check if the PM2 process is running
        echo "Checking PM2 process status..."
        ssh $SERVER_USER@$SERVER_IP "pm2 list | grep personpicker" || {
          echo "❌ PM2 process not found"
          exit 1
        }

        # Check if the server is responding
        echo "Checking if server is responding..."
        ssh $SERVER_USER@$SERVER_IP "curl -s http://localhost:4000 > /dev/null"
        if [ $? -eq 0 ]; then
          echo "✅ Server is responding"
        else
          echo "⚠️ Server is not responding on port 4000 - this may be expected if your server doesn't respond to HTTP on this port"
        fi

        echo "✅ Deployment completed successfully"

    - name: Setup monitoring and auto-restart
      env:
        SERVER_IP: ${{ secrets.SERVER_IP }}
        SERVER_USER: ${{ secrets.SERVER_USER }}
        DEPLOY_DIR: ${{ secrets.DEPLOY_DIR }}
      run: |
        echo "Setting up monitoring and auto-restart..."

        # Configure PM2 to monitor and auto-restart the application
        ssh $SERVER_USER@$SERVER_IP "pm2 set pm2:autodump true && \
          pm2 set pm2:autorestart true && \
          pm2 set pm2:autostart true && \
          pm2 save"

        # Make health check script executable
        ssh $SERVER_USER@$SERVER_IP "chmod +x $DEPLOY_DIR/multiplayer/server/health-check.sh"

        # Set up cron job to run health check every 15 minutes
        ssh $SERVER_USER@$SERVER_IP "crontab -l | grep -v 'health-check.sh' | { cat; echo '*/15 * * * * $DEPLOY_DIR/multiplayer/server/health-check.sh > /dev/null 2>&1'; } | crontab -"

        echo "✅ Monitoring and auto-restart configured"

    - name: Send deployment notification
      if: success()
      run: |
        echo "🎉 Deployment completed successfully!"
        echo "The PersonPicker website and multiplayer server have been deployed and are running."

    - name: Send failure notification
      if: failure()
      run: |
        echo "❌ Deployment failed!"
        echo "Please check the logs for more information."
