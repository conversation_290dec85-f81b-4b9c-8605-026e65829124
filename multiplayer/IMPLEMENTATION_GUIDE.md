# PersonPicker Multiplayer Implementation Guide

This guide provides step-by-step instructions for implementing multiplayer functionality in other PersonPicker games.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Step 1: Create the Game Directory Structure](#step-1-create-the-game-directory-structure)
3. [Step 2: Create the HTML File](#step-2-create-the-html-file)
4. [Step 3: Create the JavaScript File](#step-3-create-the-javascript-file)
5. [Step 4: Implement Game-Specific Logic](#step-4-implement-game-specific-logic)
6. [Step 5: Test Your Implementation](#step-5-test-your-implementation)
7. [Example: Converting Celestial Cascade](#example-converting-celestial-cascade)

## Prerequisites

Before implementing multiplayer in a game, make sure you have:

1. A working single-player version of the game
2. The multiplayer framework installed and running
3. Basic understanding of JavaScript and WebSockets

## Step 1: Create the Game Directory Structure

Create a new directory for your multiplayer game in the `multiplayer/games` directory:

```
multiplayer/
  └── games/
      └── your-game-name/
          ├── index.html
          └── your-game-name-multiplayer.js
```

## Step 2: Create the HTML File

Create an `index.html` file with the following structure:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiplayer Your Game Name</title>
    <link rel="stylesheet" href="/public/css/games/your-game-name.css">
    <link rel="stylesheet" href="/multiplayer/public/css/multiplayer.css">
    <script src="/public/js/confetti.js"></script>
</head>
<body>
    <div class="container">
        <h1>Multiplayer Your Game Name</h1>
        <div id="description">Your game description here</div>
        
        <!-- Connection Status -->
        <div id="connectionStatus" class="connection-status connection-disconnected">Disconnected</div>
        
        <!-- Lobby Section -->
        <div id="lobbySection" class="multiplayer-section">
            <div class="multiplayer-controls">
                <div id="createRoomForm">
                    <div class="form-group">
                        <label for="playerName">Your Name:</label>
                        <input type="text" id="playerName" placeholder="Enter your name">
                    </div>
                    <div class="form-row">
                        <button id="createRoomBtn" class="btn-primary">Create Room</button>
                        <button id="joinRoomBtn" class="btn-secondary">Join Room</button>
                    </div>
                </div>
                
                <div id="joinRoomForm" class="hidden">
                    <div class="form-group">
                        <label for="roomIdInput">Room ID:</label>
                        <input type="text" id="roomIdInput" placeholder="Enter room ID">
                    </div>
                    <div class="form-row">
                        <button id="confirmJoinBtn" class="btn-primary">Join</button>
                        <button id="cancelJoinBtn" class="btn-secondary">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Room Section -->
        <div id="roomSection" class="multiplayer-section hidden">
            <div class="room-info">
                <div>Room: <span id="roomId" class="room-id">ABCDEF</span></div>
                <div id="copyLink" class="copy-link">Copy Link</div>
            </div>
            
            <div id="playerList" class="player-list">
                <!-- Players will be listed here -->
            </div>
            
            <div id="hostControls" class="multiplayer-controls hidden">
                <button id="startGameBtn" class="btn-success">Start Game</button>
            </div>
            
            <div class="multiplayer-controls">
                <button id="leaveRoomBtn" class="btn-danger">Leave Room</button>
            </div>
        </div>
        
        <!-- Game Section -->
        <div id="gameSection" class="multiplayer-section hidden">
            <div id="turnIndicator" class="turn-indicator">Waiting for game to start...</div>
            
            <!-- Game-specific elements go here -->
            
            <button id="newGameBtn" class="hidden">New Game</button>
        </div>
        
        <!-- Status Message -->
        <div id="statusMessage" class="status-message hidden"></div>
    </div>
    
    <script src="/multiplayer/public/js/multiplayer-client.js"></script>
    <script src="/multiplayer/games/your-game-name/your-game-name-multiplayer.js"></script>
</body>
</html>
```

## Step 3: Create the JavaScript File

Create a JavaScript file with the following structure:

```javascript
/*!
 * Person Picker Multiplayer Your Game Name (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

// Game state
let gameState = {
    players: [],
    currentPlayerIndex: 0,
    gameStarted: false,
    gameEnded: false,
    winner: null,
    // Game-specific state
};

// Client state
let clientState = {
    clientId: null,
    playerName: '',
    roomId: null,
    isHost: false,
    isMyTurn: false
};

// DOM Elements
const elements = {
    // Sections
    lobbySection: document.getElementById('lobbySection'),
    roomSection: document.getElementById('roomSection'),
    gameSection: document.getElementById('gameSection'),
    
    // Lobby controls
    playerNameInput: document.getElementById('playerName'),
    createRoomBtn: document.getElementById('createRoomBtn'),
    joinRoomBtn: document.getElementById('joinRoomBtn'),
    joinRoomForm: document.getElementById('joinRoomForm'),
    roomIdInput: document.getElementById('roomIdInput'),
    confirmJoinBtn: document.getElementById('confirmJoinBtn'),
    cancelJoinBtn: document.getElementById('cancelJoinBtn'),
    
    // Room controls
    roomIdDisplay: document.getElementById('roomId'),
    copyLinkBtn: document.getElementById('copyLink'),
    playerList: document.getElementById('playerList'),
    hostControls: document.getElementById('hostControls'),
    startGameBtn: document.getElementById('startGameBtn'),
    leaveRoomBtn: document.getElementById('leaveRoomBtn'),
    
    // Game elements
    turnIndicator: document.getElementById('turnIndicator'),
    newGameBtn: document.getElementById('newGameBtn'),
    
    // Status
    connectionStatus: document.getElementById('connectionStatus'),
    statusMessage: document.getElementById('statusMessage'),
    
    // Game-specific elements
    // ...
};

// Initialize multiplayer client
const client = new MultiplayerClient({
    debug: true
});

// Event listeners for UI elements
elements.createRoomBtn.addEventListener('click', createRoom);
elements.joinRoomBtn.addEventListener('click', showJoinRoomForm);
elements.confirmJoinBtn.addEventListener('click', joinRoom);
elements.cancelJoinBtn.addEventListener('click', hideJoinRoomForm);
elements.copyLinkBtn.addEventListener('click', copyRoomLink);
elements.startGameBtn.addEventListener('click', startGame);
elements.leaveRoomBtn.addEventListener('click', leaveRoom);
elements.newGameBtn.addEventListener('click', requestNewGame);

// Initialize the application
function init() {
    // Try to load player name from localStorage
    const savedName = localStorage.getItem('playerName');
    if (savedName) {
        elements.playerNameInput.value = savedName;
    }
    
    // Connect to the WebSocket server
    connectToServer();
}

// Connect to the WebSocket server
function connectToServer() {
    updateConnectionStatus('connecting');
    
    client.connect()
        .then(() => {
            updateConnectionStatus('connected');
        })
        .catch(error => {
            console.error('Failed to connect:', error);
            updateConnectionStatus('disconnected');
            showStatusMessage('Failed to connect to server. Please try again.', 'error');
        });
}

// Create a new game room
function createRoom() {
    const playerName = elements.playerNameInput.value.trim();
    if (!playerName) {
        showStatusMessage('Please enter your name', 'error');
        return;
    }
    
    // Save player name to localStorage
    localStorage.setItem('playerName', playerName);
    clientState.playerName = playerName;
    
    // Create room
    client.createRoom('your-game-name', playerName);
}

// Show the join room form
function showJoinRoomForm() {
    elements.joinRoomForm.classList.remove('hidden');
}

// Hide the join room form
function hideJoinRoomForm() {
    elements.joinRoomForm.classList.add('hidden');
}

// Join an existing room
function joinRoom() {
    const playerName = elements.playerNameInput.value.trim();
    const roomId = elements.roomIdInput.value.trim();
    
    if (!playerName) {
        showStatusMessage('Please enter your name', 'error');
        return;
    }
    
    if (!roomId) {
        showStatusMessage('Please enter a room ID', 'error');
        return;
    }
    
    // Save player name to localStorage
    localStorage.setItem('playerName', playerName);
    clientState.playerName = playerName;
    
    // Join room
    client.joinRoom(roomId, playerName);
}

// Copy room link to clipboard
function copyRoomLink() {
    const roomId = clientState.roomId;
    const url = `${window.location.origin}${window.location.pathname}?roomId=${roomId}`;
    
    navigator.clipboard.writeText(url)
        .then(() => {
            showStatusMessage('Room link copied to clipboard', 'success');
        })
        .catch(err => {
            console.error('Failed to copy:', err);
            showStatusMessage('Failed to copy link', 'error');
        });
}

// Leave the current room
function leaveRoom() {
    client.leaveRoom();
    showLobby();
}

// Start the game (host only)
function startGame() {
    if (!clientState.isHost) return;
    
    const players = client.room.players.map(p => p.name);
    if (players.length < 2) {
        showStatusMessage('Need at least 2 players to start', 'error');
        return;
    }
    
    // Initialize game state
    const initialState = {
        players: players,
        currentPlayerIndex: 0,
        gameStarted: true,
        gameEnded: false,
        winner: null,
        // Game-specific initialization
    };
    
    // Start the game
    client.startGame(initialState);
}

// Request a new game after the current one ends
function requestNewGame() {
    if (!clientState.isHost) return;
    
    // Reset game state
    const players = client.room.players.map(p => p.name);
    const newGameState = {
        players: players,
        currentPlayerIndex: 0,
        gameStarted: true,
        gameEnded: false,
        winner: null,
        // Game-specific initialization
    };
    
    // Send game action to start a new game
    client.sendGameAction('new_game', newGameState);
}

// Update the player list
function updatePlayerList() {
    if (!client.room) return;
    
    const players = client.room.players;
    elements.playerList.innerHTML = '';
    
    players.forEach(player => {
        const playerItem = document.createElement('div');
        playerItem.className = 'player-item';
        
        const nameSpan = document.createElement('span');
        nameSpan.className = 'player-name';
        nameSpan.textContent = player.name;
        
        const statusSpan = document.createElement('span');
        if (player.isHost) {
            statusSpan.className = 'player-host';
            statusSpan.textContent = 'Host';
        }
        
        if (player.id === clientState.clientId) {
            nameSpan.textContent += ' (You)';
        }
        
        playerItem.appendChild(nameSpan);
        playerItem.appendChild(statusSpan);
        elements.playerList.appendChild(playerItem);
    });
}

// Show the lobby section
function showLobby() {
    elements.lobbySection.classList.remove('hidden');
    elements.roomSection.classList.add('hidden');
    elements.gameSection.classList.add('hidden');
}

// Show the room section
function showRoom() {
    elements.lobbySection.classList.add('hidden');
    elements.roomSection.classList.remove('hidden');
    elements.gameSection.classList.add('hidden');
}

// Show the game section
function showGame() {
    elements.lobbySection.classList.add('hidden');
    elements.roomSection.classList.add('hidden');
    elements.gameSection.classList.remove('hidden');
}

// Update connection status display
function updateConnectionStatus(status) {
    elements.connectionStatus.className = 'connection-status';
    
    switch (status) {
        case 'connected':
            elements.connectionStatus.classList.add('connection-connected');
            elements.connectionStatus.textContent = 'Connected';
            break;
        case 'disconnected':
            elements.connectionStatus.classList.add('connection-disconnected');
            elements.connectionStatus.textContent = 'Disconnected';
            break;
        case 'connecting':
            elements.connectionStatus.classList.add('connection-connecting');
            elements.connectionStatus.textContent = 'Connecting...';
            break;
    }
}

// Show a status message
function showStatusMessage(message, type = 'info') {
    elements.statusMessage.textContent = message;
    elements.statusMessage.className = 'status-message fade-in';
    elements.statusMessage.classList.add(`status-${type}`);
    elements.statusMessage.classList.remove('hidden');
    
    setTimeout(() => {
        elements.statusMessage.classList.add('hidden');
    }, 3000);
}

// Event handlers for WebSocket events
client.on('connected', (data) => {
    clientState.clientId = data.clientId;
    
    // Check if there's a room ID in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const roomId = urlParams.get('roomId');
    if (roomId) {
        elements.roomIdInput.value = roomId;
        showJoinRoomForm();
    }
});

client.on('room_created', (data) => {
    clientState.roomId = data.roomId;
    clientState.isHost = true;
    elements.roomIdDisplay.textContent = data.roomId;
    elements.hostControls.classList.remove('hidden');
    updatePlayerList();
    showRoom();
});

client.on('player_joined', (data) => {
    updatePlayerList();
    showStatusMessage(`${data.player.name} joined the room`, 'info');
});

client.on('player_left', (data) => {
    updatePlayerList();
    showStatusMessage(`A player left the room`, 'warning');
});

client.on('game_started', (data) => {
    gameState = data.room.gameState;
    // Initialize game UI
    // ...
    showGame();
});

client.on('game_action', (data) => {
    // Handle game actions
    // ...
});

client.on('error', (data) => {
    showStatusMessage(data.message, 'error');
});

client.on('disconnected', () => {
    updateConnectionStatus('disconnected');
    showStatusMessage('Disconnected from server. Trying to reconnect...', 'error');
});

client.on('reconnected', () => {
    updateConnectionStatus('connected');
    showStatusMessage('Reconnected to server', 'success');
});

// Initialize the application
init();
```

## Step 4: Implement Game-Specific Logic

Now, implement the game-specific logic:

1. **Define Game State**: Add game-specific properties to the `gameState` object
2. **Create Game UI**: Add game-specific elements to the HTML and reference them in the JavaScript
3. **Implement Game Logic**: Add functions for game mechanics, turn handling, and win conditions
4. **Handle Game Actions**: Implement handlers for game-specific actions in the `client.on('game_action', ...)` event

## Step 5: Test Your Implementation

1. Start the WebSocket server:
   ```
   cd multiplayer/server
   npm install
   node server.js
   ```

2. Open your game in multiple browser windows:
   ```
   http://localhost:8080/multiplayer/games/your-game-name/
   ```

3. Create a room in one window and join it from the others
4. Test all game functionality, especially turn handling and state synchronization

## Example: Converting Celestial Cascade

Here's a simplified example of how to convert the Celestial Cascade game to multiplayer:

### Game State

```javascript
let gameState = {
    players: [],
    currentPlayerIndex: 0,
    gameStarted: false,
    gameEnded: false,
    winner: null,
    celestialBodies: [],
    lastLanded: null
};
```

### Game-Specific UI Elements

```javascript
// Add to elements object
gameCanvas: document.getElementById('gameCanvas'),
launchButton: document.getElementById('launchButton'),
resultDiv: document.getElementById('resultDiv')
```

### Game Initialization

```javascript
function initializeGame() {
    // Set up canvas
    elements.gameCanvas.width = elements.gameCanvas.offsetWidth;
    elements.gameCanvas.height = elements.gameCanvas.offsetHeight;
    
    // Create stars
    createStars();
    
    // Start game loop
    requestAnimationFrame(gameLoop);
}

function startGame() {
    if (!clientState.isHost) return;
    
    const players = client.room.players.map(p => p.name);
    if (players.length < 2) {
        showStatusMessage('Need at least 2 players to start', 'error');
        return;
    }
    
    // Initialize celestial bodies for each player
    const celestialBodies = players.map(player => ({
        name: player,
        x: Math.random() * elements.gameCanvas.width,
        y: 0,
        radius: 20,
        color: getRandomColor(),
        speed: 1 + Math.random(),
        landed: false
    }));
    
    const initialState = {
        players: players,
        currentPlayerIndex: 0,
        gameStarted: true,
        gameEnded: false,
        winner: null,
        celestialBodies: celestialBodies,
        lastLanded: null
    };
    
    client.startGame(initialState);
}
```

### Game Loop and Actions

```javascript
function gameLoop() {
    // Clear canvas
    const ctx = elements.gameCanvas.getContext('2d');
    ctx.clearRect(0, 0, elements.gameCanvas.width, elements.gameCanvas.height);
    
    // Draw stars
    drawStars();
    
    // Update and draw celestial bodies
    if (gameState.gameStarted && !gameState.gameEnded) {
        updateCelestialBodies();
        drawCelestialBodies();
        
        // Check if game is over
        if (gameState.celestialBodies.every(body => body.landed)) {
            endGame();
        }
    }
    
    requestAnimationFrame(gameLoop);
}

function updateCelestialBodies() {
    // Only the host updates positions to ensure consistency
    if (clientState.isHost) {
        let updated = false;
        
        gameState.celestialBodies.forEach(body => {
            if (!body.landed) {
                body.y += body.speed;
                
                if (body.y > elements.gameCanvas.height - body.radius) {
                    body.y = elements.gameCanvas.height - body.radius;
                    body.landed = true;
                    gameState.lastLanded = body;
                    updated = true;
                }
            }
        });
        
        // Send updated state if needed
        if (updated) {
            client.sendGameAction('update_bodies', gameState);
        }
    }
}

function endGame() {
    if (clientState.isHost && !gameState.gameEnded) {
        gameState.gameEnded = true;
        gameState.winner = gameState.lastLanded.name;
        
        client.sendGameAction('game_over', gameState);
    }
}
```

### Event Handlers

```javascript
client.on('game_action', (data) => {
    if (data.action === 'update_bodies') {
        gameState.celestialBodies = data.gameState.celestialBodies;
        gameState.lastLanded = data.gameState.lastLanded;
    } else if (data.action === 'game_over') {
        gameState.gameEnded = true;
        gameState.winner = data.gameState.winner;
        
        elements.resultDiv.textContent = `${gameState.winner} wins!`;
        elements.resultDiv.classList.add('celebrate');
        
        confetti?.({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 }
        });
        
        if (clientState.isHost) {
            elements.newGameBtn.classList.remove('hidden');
        }
    } else if (data.action === 'new_game') {
        gameState = data.gameState;
        elements.resultDiv.textContent = '';
        elements.resultDiv.classList.remove('celebrate');
        elements.newGameBtn.classList.add('hidden');
    }
});
```

This example demonstrates the key aspects of converting a game to multiplayer, focusing on state synchronization and turn management.
