/*!
 * Person Picker Multiplayer Server (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

const http = require('http');
const express = require('express');
const WebSocket = require('ws');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Create Express app
const app = express();
const PORT = process.env.PORT || 8080;

// Serve static files from the project root
app.use(express.static(path.join(__dirname, '../..')));

// Create HTTP server
const server = http.createServer(app);

// Create WebSocket server using the HTTP server
const wss = new WebSocket.Server({ server });

// Store active game rooms
const gameRooms = new Map();

// Handle WebSocket connections
wss.on('connection', (ws) => {
    // Assign a unique client ID
    ws.id = uuidv4();
    ws.isAlive = true;

    console.log(`Client connected: ${ws.id}`);

    // Handle ping/pong for connection health check
    ws.on('pong', () => {
        ws.isAlive = true;
    });

    // Handle incoming messages
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            handleMessage(ws, data);
        } catch (error) {
            console.error('Error parsing message:', error);
            sendToClient(ws, {
                type: 'error',
                message: 'Invalid message format'
            });
        }
    });

    // Handle client disconnection
    ws.on('close', () => {
        console.log(`Client disconnected: ${ws.id}`);
        handleClientDisconnect(ws);
    });

    // Send initial connection confirmation
    sendToClient(ws, {
        type: 'connected',
        clientId: ws.id
    });
});

// Ping clients every 30 seconds to check connection
const interval = setInterval(() => {
    wss.clients.forEach((ws) => {
        if (ws.isAlive === false) {
            console.log(`Client timed out: ${ws.id}`);
            handleClientDisconnect(ws);
            return ws.terminate();
        }

        ws.isAlive = false;
        ws.ping();
    });
}, 30000);

// Clean up interval on server close
wss.on('close', () => {
    clearInterval(interval);
});

// Handle client messages
function handleMessage(client, data) {
    console.log(`Received message from ${client.id}:`, data);

    switch (data.type) {
        case 'create_room':
            createRoom(client, data);
            break;
        case 'join_room':
            joinRoom(client, data);
            break;
        case 'leave_room':
            leaveRoom(client);
            break;
        case 'list_rooms':
            listRooms(client);
            break;
        case 'game_action':
            handleGameAction(client, data);
            break;
        case 'start_game':
            startGame(client, data);
            break;
        default:
            sendToClient(client, {
                type: 'error',
                message: 'Unknown message type'
            });
    }
}

// Create a new game room
function createRoom(client, data) {
    // Generate a unique room ID
    const roomId = uuidv4().substring(0, 6).toUpperCase();

    // Create the room
    const room = {
        id: roomId,
        host: client.id,
        game: data.game,
        players: [{
            id: client.id,
            name: data.playerName,
            emoji: data.emoji || "👤", // Default emoji if none provided
            isHost: true
        }],
        state: 'waiting', // waiting, playing, ended
        gameState: null,
        createdAt: new Date().toISOString()
    };

    // Store the room
    gameRooms.set(roomId, room);

    // Associate the client with the room
    client.roomId = roomId;

    // Notify the client
    sendToClient(client, {
        type: 'room_created',
        roomId: roomId,
        room: room
    });

    console.log(`Room created: ${roomId} by ${client.id}`);
}

// Join an existing game room
function joinRoom(client, data) {
    const roomId = data.roomId;
    const room = gameRooms.get(roomId);

    // Check if the room exists
    if (!room) {
        return sendToClient(client, {
            type: 'error',
            message: 'Room not found'
        });
    }

    // Add the player to the room
    room.players.push({
        id: client.id,
        name: data.playerName,
        emoji: data.emoji || "👤", // Default emoji if none provided
        isHost: false
    });

    // Associate the client with the room
    client.roomId = roomId;

    // First, notify the joining player that they successfully joined
    sendToClient(client, {
        type: 'room_joined',
        roomId: roomId,
        room: room
    });

    // Then, notify all clients in the room (including the joining player) about the new player
    broadcastToRoom(roomId, {
        type: 'player_joined',
        player: {
            id: client.id,
            name: data.playerName,
            emoji: data.emoji || "👤" // Include emoji in the notification
        },
        room: room
    });

    console.log(`Player ${client.id} joined room ${roomId}`);
}

// List available game rooms
function listRooms(client) {
    const availableRooms = [];
    
    gameRooms.forEach((room, roomId) => {
        // Only include rooms that are waiting for players (not playing or ended)
        if (room.state === 'waiting') {
            availableRooms.push({
                id: room.id,
                game: room.game,
                playerCount: room.players.length,
                hostName: room.players.find(p => p.isHost)?.name || 'Unknown',
                createdAt: room.createdAt || new Date().toISOString()
            });
        }
    });

    sendToClient(client, {
        type: 'rooms_list',
        rooms: availableRooms
    });

    console.log(`Sent ${availableRooms.length} available rooms to ${client.id}`);
}

// Leave a game room
function leaveRoom(client) {
    const roomId = client.roomId;
    if (!roomId) return;

    const room = gameRooms.get(roomId);
    if (!room) return;

    // Remove the player from the room
    const playerIndex = room.players.findIndex(p => p.id === client.id);
    if (playerIndex !== -1) {
        const player = room.players[playerIndex];
        room.players.splice(playerIndex, 1);

        // If the host left, assign a new host or close the room
        if (player.isHost) {
            if (room.players.length > 0) {
                room.players[0].isHost = true;
                room.host = room.players[0].id;
            } else {
                // Close the room if no players left
                gameRooms.delete(roomId);
                console.log(`Room closed: ${roomId}`);
                return;
            }
        }

        // Notify remaining clients
        broadcastToRoom(roomId, {
            type: 'player_left',
            playerId: client.id,
            room: room
        });

        console.log(`Player ${client.id} left room ${roomId}`);
    }

    // Remove room association from client
    client.roomId = null;
}

// Handle game-specific actions
function handleGameAction(client, data) {
    const roomId = client.roomId;
    if (!roomId) return;

    const room = gameRooms.get(roomId);
    if (!room) return;

    // Update game state based on the action
    if (data.gameState) {
        room.gameState = data.gameState;
    }

    // Broadcast the action to all clients in the room
    broadcastToRoom(roomId, {
        type: 'game_action',
        playerId: client.id,
        action: data.action,
        gameState: room.gameState
    });
}

// Start a game in a room
function startGame(client, data) {
    const roomId = client.roomId;
    if (!roomId) return;

    const room = gameRooms.get(roomId);
    if (!room) return;

    // Only the host can start the game
    if (room.host !== client.id) {
        return sendToClient(client, {
            type: 'error',
            message: 'Only the host can start the game'
        });
    }

    // Update room state
    room.state = 'playing';
    room.gameState = data.initialState || {};

    // Broadcast game start to all clients in the room
    broadcastToRoom(roomId, {
        type: 'game_started',
        room: room
    });

    console.log(`Game started in room ${roomId}`);
}

// Handle client disconnection
function handleClientDisconnect(client) {
    leaveRoom(client);
}

// Send a message to a specific client
function sendToClient(client, message) {
    if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(message));
    }
}

// Broadcast a message to all clients in a room
function broadcastToRoom(roomId, message) {
    const room = gameRooms.get(roomId);
    if (!room) return;

    wss.clients.forEach((client) => {
        if (client.roomId === roomId && client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify(message));
        }
    });
}

// Start the HTTP server
server.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
    console.log(`WebSocket server running on ws://localhost:${PORT}`);
});
