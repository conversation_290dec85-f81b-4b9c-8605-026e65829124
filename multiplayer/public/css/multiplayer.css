/*!
 * Person Picker Multiplayer (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

:root {
    --primary-color: #18314F;
    --secondary-color: #384E77;
    --accent-color: #8BBEB2;
    --danger-color: #0D0630;
    --success-color: #4CAF50;
    --warning-color: #FFC107;
    --text-color: #0D0630;
    --background-color: #FFFFFF;
    --card-background: #FFFFFF;
    --input-background: #FFFFFF;
    --input-border: #8BBEB2;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    color: var(--text-color);
}

.container {
    background-color: var(--card-background);
    border-radius: 20px;
    padding: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 95%;
    width: 600px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

h1 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

h2 {
    text-align: center;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

#description {
    text-align: center;
    color: var(--text-color);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

/* Multiplayer specific styles */
.multiplayer-section {
    width: 100%;
    margin-bottom: 1rem;
}

.multiplayer-controls {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
}

.form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 0.5rem;
}

.form-group label {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.form-row {
    display: flex;
    gap: 0.5rem;
    width: 100%;
}

.form-row > * {
    flex: 1;
}

input, button {
    padding: 0.5rem;
    border-radius: 5px;
    border: 1px solid var(--input-border);
    font-family: 'Poppins', sans-serif;
}

button {
    cursor: pointer;
    background-color: var(--accent-color);
    color: white;
    border: none;
    transition: background-color 0.3s ease, transform 0.1s ease;
}

button:hover {
    opacity: 0.9;
}

button:active {
    transform: scale(0.98);
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    opacity: 0.7;
}

.btn-primary {
    background-color: var(--accent-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
}

.btn-danger {
    background-color: var(--danger-color);
}

.btn-success {
    background-color: var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--text-color);
}

.player-list {
    width: 100%;
    margin: 1rem 0;
}

.player-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border-radius: 5px;
    background-color: var(--input-background);
    margin-bottom: 0.5rem;
}

.player-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.player-emoji {
    font-size: 1.2rem;
    margin-right: 0.25rem;
}

.player-name {
    font-weight: 600;
}

.player-host {
    color: var(--accent-color);
    font-size: 0.8rem;
}

.player-current {
    color: var(--success-color);
    font-size: 0.8rem;
}

.room-info {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0.5rem;
    background-color: var(--secondary-color);
    color: white;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.room-id {
    font-weight: 600;
    font-size: 1.2rem;
}

.copy-link {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.status-message {
    width: 100%;
    padding: 0.5rem;
    border-radius: 5px;
    margin-bottom: 1rem;
    text-align: center;
}

.status-success {
    background-color: var(--success-color);
    color: white;
}

.status-error {
    background-color: var(--danger-color);
    color: white;
}

.status-warning {
    background-color: var(--warning-color);
    color: var(--text-color);
}

.status-info {
    background-color: var(--secondary-color);
    color: white;
}

.hidden {
    display: none !important;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .container {
        width: 95%;
        padding: 0.75rem;
    }

    .form-row {
        flex-direction: column;
    }
}

/* Animation for status messages */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* Emoji selector styles */
.emoji-selector-container {
    margin-top: 0.5rem;
    margin-bottom: 1rem;
}

.emoji-selector-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.emoji-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    background-color: var(--input-background);
    padding: 0.5rem;
    border-radius: 5px;
    max-width: 100%;
}

.emoji-option {
    font-size: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 5px;
    transition: all 0.2s ease;
}

.emoji-option:hover {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
}

.emoji-option.selected {
    background-color: var(--accent-color);
    color: white;
}

/* Game board styles */
.game-board {
    width: 100%;
    margin: 1rem 0;
}

/* Turn indicator */
.turn-indicator {
    padding: 0.5rem;
    background-color: var(--accent-color);
    color: white;
    border-radius: 5px;
    text-align: center;
    margin-bottom: 1rem;
    font-weight: 600;
}

/* Connection status indicator */
.connection-status {
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
    font-size: 0.8rem;
    z-index: 1000;
}

.connection-connected {
    background-color: var(--success-color);
    color: white;
}

.connection-disconnected {
    background-color: var(--danger-color);
    color: white;
}

.connection-connecting {
    background-color: var(--warning-color);
    color: var(--text-color);
}
