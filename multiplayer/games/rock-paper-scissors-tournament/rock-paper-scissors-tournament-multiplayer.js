/*!
 * Person Picker Multiplayer Rock Paper Scissors Tournament (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

// Game state
let gameState = {
    players: [],
    matches: [],
    currentRound: 0,
    gameStarted: false,
    gameEnded: false,
    winner: null,
    tournamentBracket: []
};

// Client state
let clientState = {
    clientId: null,
    playerName: '',
    selectedEmoji: '👤', // Default emoji
    roomId: null,
    isHost: false,
    currentMatch: null,
    myChoice: null,
    isEliminated: false
};

// Available emojis for selection
const availableEmojis = [
    '😀', '😎', '🤠', '👻', '🐱', '🐶', '🦊', '🐼',
    '🐨', '🦁', '🐯', '🦄', '🐙', '🦋', '🌟'
];

// Constants
const choices = ['✊', '✋', '✌️'];

// DOM Elements
const elements = {
    // Sections
    lobbySection: document.getElementById('lobbySection'),
    roomSection: document.getElementById('roomSection'),
    gameSection: document.getElementById('gameSection'),

    // Lobby controls
    playerNameInput: document.getElementById('playerName'),
    emojiSelector: document.getElementById('emojiSelector'),
    joinEmojiSelector: document.getElementById('joinEmojiSelector'),
    createRoomBtn: document.getElementById('createRoomBtn'),
    joinRoomBtn: document.getElementById('joinRoomBtn'),
    joinRoomForm: document.getElementById('joinRoomForm'),
    roomIdInput: document.getElementById('roomIdInput'),
    confirmJoinBtn: document.getElementById('confirmJoinBtn'),
    cancelJoinBtn: document.getElementById('cancelJoinBtn'),

    // Room controls
    roomIdDisplay: document.getElementById('roomId'),
    copyLinkBtn: document.getElementById('copyLink'),
    playerList: document.getElementById('playerList'),
    hostControls: document.getElementById('hostControls'),
    startGameBtn: document.getElementById('startGameBtn'),
    leaveRoomBtn: document.getElementById('leaveRoomBtn'),

    // Game elements
    turnIndicator: document.getElementById('turnIndicator'),
    tournament: document.getElementById('tournament'),
    countdown: document.getElementById('countdown'),
    finalResult: document.getElementById('finalResult'),
    message: document.getElementById('message'),
    newGameBtn: document.getElementById('newGameBtn'),

    // Match elements
    currentMatch: document.getElementById('currentMatch'),
    matchInfo: document.getElementById('matchInfo'),
    playerChoiceControls: document.getElementById('playerChoiceControls'),
    waitingMessage: document.getElementById('waitingMessage'),

    // Status
    connectionStatus: document.getElementById('connectionStatus'),
    statusMessage: document.getElementById('statusMessage')
};

// Initialize multiplayer client
const client = new MultiplayerClient({
    debug: true
});

// Initialize the application
function init() {
    // Add event listeners for UI elements after DOM is ready
    if (elements.createRoomBtn) elements.createRoomBtn.addEventListener('click', createRoom);
    if (elements.joinRoomBtn) elements.joinRoomBtn.addEventListener('click', showJoinRoomForm);
    if (elements.confirmJoinBtn) elements.confirmJoinBtn.addEventListener('click', joinRoom);
    if (elements.cancelJoinBtn) elements.cancelJoinBtn.addEventListener('click', hideJoinRoomForm);
    if (elements.copyLinkBtn) elements.copyLinkBtn.addEventListener('click', copyRoomLink);
    if (elements.startGameBtn) elements.startGameBtn.addEventListener('click', startTournament);
    if (elements.leaveRoomBtn) elements.leaveRoomBtn.addEventListener('click', leaveRoom);
    if (elements.newGameBtn) elements.newGameBtn.addEventListener('click', requestNewTournament);

    // Add event listeners for choice buttons
    document.querySelectorAll('.choice-btn').forEach(button => {
        button.addEventListener('click', () => {
            makeChoice(button.dataset.choice);
        });
    });

    // Try to load player name from localStorage
    const savedName = localStorage.getItem('playerName');
    if (savedName && elements.playerNameInput) {
        elements.playerNameInput.value = savedName;
    }

    // Try to load selected emoji from localStorage
    const savedEmoji = localStorage.getItem('selectedEmoji');
    if (savedEmoji) {
        clientState.selectedEmoji = savedEmoji;
    }

    // Initialize emoji selector
    initEmojiSelector();

    // Connect to the WebSocket server
    connectToServer();
}

// Initialize the emoji selector
function initEmojiSelector() {
    // Initialize main emoji selector
    initSingleEmojiSelector(elements.emojiSelector);

    // Initialize join form emoji selector
    initSingleEmojiSelector(elements.joinEmojiSelector);
}

// Initialize a single emoji selector
function initSingleEmojiSelector(selectorElement) {
    if (!selectorElement) return;

    // Clear existing content
    selectorElement.innerHTML = '';

    // Add emoji options
    availableEmojis.forEach(emoji => {
        const emojiElement = document.createElement('div');
        emojiElement.className = 'emoji-option';
        if (emoji === clientState.selectedEmoji) {
            emojiElement.classList.add('selected');
        }
        emojiElement.textContent = emoji;
        emojiElement.addEventListener('click', () => selectEmoji(emoji));
        selectorElement.appendChild(emojiElement);
    });
}

// Handle emoji selection
function selectEmoji(emoji) {
    // Update selected emoji
    clientState.selectedEmoji = emoji;

    // Save to localStorage
    localStorage.setItem('selectedEmoji', emoji);

    // Update both selectors
    updateEmojiSelectorUI(elements.emojiSelector, emoji);
    updateEmojiSelectorUI(elements.joinEmojiSelector, emoji);
}

// Update the UI of an emoji selector
function updateEmojiSelectorUI(selectorElement, selectedEmoji) {
    if (!selectorElement) return;

    const options = selectorElement.querySelectorAll('.emoji-option');
    options.forEach(el => {
        el.classList.remove('selected');
        if (el.textContent === selectedEmoji) {
            el.classList.add('selected');
        }
    });
}

// Connect to the WebSocket server
function connectToServer() {
    updateConnectionStatus('connecting');

    client.connect()
        .then(() => {
            updateConnectionStatus('connected');
        })
        .catch(error => {
            console.error('Failed to connect:', error);
            updateConnectionStatus('disconnected');
            showStatusMessage('Failed to connect to server. Please try again.', 'error');
        });
}

// Create a new game room
function createRoom() {
    const playerName = elements.playerNameInput.value.trim();
    if (!playerName) {
        showStatusMessage('Please enter your name', 'error');
        return;
    }

    // Save player name to localStorage
    localStorage.setItem('playerName', playerName);
    clientState.playerName = playerName;

    // Create room with selected emoji
    client.createRoom('rock-paper-scissors-tournament', playerName, clientState.selectedEmoji);
}

// Show the join room form
function showJoinRoomForm() {
    elements.joinRoomForm.classList.remove('hidden');

    // Make sure the emoji selector is initialized with the current selection
    updateEmojiSelectorUI(elements.joinEmojiSelector, clientState.selectedEmoji);
}

// Hide the join room form
function hideJoinRoomForm() {
    elements.joinRoomForm.classList.add('hidden');
    elements.roomIdInput.value = '';
}

// Join an existing room
function joinRoom() {
    const playerName = elements.playerNameInput.value.trim();
    const roomId = elements.roomIdInput.value.trim();

    if (!playerName) {
        showStatusMessage('Please enter your name', 'error');
        return;
    }

    if (!roomId) {
        showStatusMessage('Please enter a room ID', 'error');
        return;
    }

    // Save player name to localStorage
    localStorage.setItem('playerName', playerName);
    clientState.playerName = playerName;

    // Join room with selected emoji
    client.joinRoom(roomId, playerName, clientState.selectedEmoji);
}

// Copy room link to clipboard
function copyRoomLink() {
    const roomId = clientState.roomId;
    const url = `${window.location.origin}${window.location.pathname}?roomId=${roomId}`;

    navigator.clipboard.writeText(url)
        .then(() => {
            showStatusMessage('Room link copied to clipboard', 'success');
        })
        .catch(err => {
            console.error('Failed to copy:', err);
            showStatusMessage('Failed to copy link', 'error');
        });
}

// Leave the current room
function leaveRoom() {
    client.leaveRoom();
    showLobby();
}

// Start the tournament (host only)
function startTournament() {
    if (!clientState.isHost) return;

    const players = client.room.players.map(p => p.name);
    if (players.length < 2) {
        showStatusMessage('Need at least 2 players to start', 'error');
        return;
    }

    // Shuffle players
    const shuffledPlayers = [...players];
    shuffleArray(shuffledPlayers);

    // Initialize tournament bracket
    const tournamentBracket = createTournamentBracket(shuffledPlayers);

    // Initialize game state
    const initialState = {
        players: players,
        matches: generateFirstRoundMatches(tournamentBracket[0]),
        currentRound: 0,
        gameStarted: true,
        gameEnded: false,
        winner: null,
        tournamentBracket: tournamentBracket
    };

    // Start the tournament
    client.startGame(initialState);
}

// Request a new tournament after the current one ends
function requestNewTournament() {
    if (!clientState.isHost) return;

    // Reset game state
    const players = client.room.players.map(p => p.name);

    // Shuffle players
    const shuffledPlayers = [...players];
    shuffleArray(shuffledPlayers);

    // Initialize tournament bracket
    const tournamentBracket = createTournamentBracket(shuffledPlayers);

    const newGameState = {
        players: players,
        matches: generateFirstRoundMatches(tournamentBracket[0]),
        currentRound: 0,
        gameStarted: true,
        gameEnded: false,
        winner: null,
        tournamentBracket: tournamentBracket
    };

    // Send game action to start a new tournament
    client.sendGameAction('new_tournament', newGameState);
}

// Make a choice in a match
function makeChoice(choice) {
    if (!gameState.gameStarted || gameState.gameEnded) return;
    if (!clientState.currentMatch) return;
    if (clientState.myChoice) return; // Already made a choice

    // Update client state
    clientState.myChoice = choice;

    // Update UI
    document.querySelectorAll('.choice-btn').forEach(btn => {
        btn.classList.remove('selected');
        if (btn.dataset.choice === choice) {
            btn.classList.add('selected');
        }
    });

    // Hide choice controls and show waiting message
    elements.playerChoiceControls.classList.add('hidden');
    elements.waitingMessage.classList.remove('hidden');

    // Send the choice to the server
    client.sendGameAction('make_choice', {
        playerName: clientState.playerName,
        matchId: clientState.currentMatch.id,
        choice: choice
    });
}

// Create tournament bracket from players
function createTournamentBracket(players) {
    const bracket = [];
    let currentRound = [...players];

    // Add dummy player if odd number
    if (currentRound.length % 2 !== 0) {
        currentRound.push('Odd One Out');
    }

    bracket.push(currentRound);

    // Create subsequent rounds
    while (currentRound.length > 1) {
        const nextRound = [];
        for (let i = 0; i < currentRound.length; i += 2) {
            nextRound.push(null); // Placeholder for winner
        }
        bracket.push(nextRound);
        currentRound = nextRound;
    }

    return bracket;
}

// Generate first round matches
function generateFirstRoundMatches(players) {
    const matches = [];

    for (let i = 0; i < players.length; i += 2) {
        const player1 = players[i];
        const player2 = players[i + 1] || 'Odd One Out';

        matches.push({
            id: `match-${i/2}-round-0`,
            player1: player1,
            player2: player2,
            player1Choice: null,
            player2Choice: null,
            winner: player2 === 'Odd One Out' ? player1 : null,
            completed: player2 === 'Odd One Out'
        });
    }

    return matches;
}

// Determine winner of a match
function determineWinner(choice1, choice2) {
    if (choice1 === choice2) return null; // Tie

    if (
        (choice1 === '✊' && choice2 === '✌️') ||
        (choice1 === '✋' && choice2 === '✊') ||
        (choice1 === '✌️' && choice2 === '✋')
    ) {
        return 0; // Player 1 wins
    }

    return 1; // Player 2 wins
}

// Process match results and update tournament bracket
function processMatchResults() {
    // Check if all matches in the current round are completed
    const allMatchesCompleted = gameState.matches.every(match => match.completed);

    if (allMatchesCompleted) {
        // Get winners from current round
        const winners = gameState.matches.map(match => match.winner);

        // Update tournament bracket
        gameState.tournamentBracket[gameState.currentRound + 1] = winners;

        // Check if tournament is over
        if (winners.length === 1) {
            // Tournament is over
            gameState.gameEnded = true;
            gameState.winner = winners[0];
        } else {
            // Move to next round
            gameState.currentRound++;

            // Generate matches for next round
            gameState.matches = [];
            for (let i = 0; i < winners.length; i += 2) {
                const player1 = winners[i];
                const player2 = winners[i + 1] || 'Odd One Out';

                gameState.matches.push({
                    id: `match-${i/2}-round-${gameState.currentRound}`,
                    player1: player1,
                    player2: player2,
                    player1Choice: null,
                    player2Choice: null,
                    winner: player2 === 'Odd One Out' ? player1 : null,
                    completed: player2 === 'Odd One Out'
                });
            }
        }

        // Reset client state for next round
        clientState.currentMatch = null;
        clientState.myChoice = null;

        // Update UI
        updateTournamentDisplay();
        updateMatchDisplay();

        // Send updated game state
        if (clientState.isHost) {
            client.sendGameAction('update_tournament', gameState);
        }
    }
}

// Create a player element for a match
function createPlayerElement(player, choice) {
    const playerElement = document.createElement('div');
    playerElement.className = 'player';

    const nameElement = document.createElement('span');
    nameElement.className = 'player-name';
    nameElement.textContent = player;

    const choiceElement = document.createElement('span');
    choiceElement.className = 'choice';
    choiceElement.textContent = choice;

    playerElement.appendChild(nameElement);
    playerElement.appendChild(choiceElement);

    return playerElement;
}

// Update live choice displays as players make choices
function updateLiveChoices(match) {
    const matchElement = document.querySelector(`[data-match-id="${match.id}"]`);
    if (!matchElement) return;

    const player1Element = matchElement.children[0];
    const player2Element = matchElement.children[1];

    if (player1Element && player2Element) {
        const choice1Element = player1Element.querySelector('.choice');
        const choice2Element = player2Element.querySelector('.choice');

        // Update choice displays with some visual feedback
        if (match.player1Choice) {
            choice1Element.textContent = '✓';
            choice1Element.style.backgroundColor = 'var(--success-color)';
            choice1Element.classList.add('choice-made');
        }

        if (match.player2Choice) {
            choice2Element.textContent = '✓';
            choice2Element.style.backgroundColor = 'var(--success-color)';
            choice2Element.classList.add('choice-made');
        }
    }
}

// Animate match result with choice reveals and winner effects
async function animateMatchResult(matchElement, match) {
    if (!matchElement || !match) return;

    const player1Element = matchElement.children[0];
    const player2Element = matchElement.children[1];
    const resultElement = matchElement.querySelector('.match-result');

    if (!player1Element || !player2Element) return;

    const choice1Element = player1Element.querySelector('.choice');
    const choice2Element = player2Element.querySelector('.choice');

    // Add bouncing animation while "thinking"
    choice1Element.classList.add('bouncing');
    choice2Element.classList.add('bouncing');
    choice1Element.textContent = '?';
    choice2Element.textContent = '?';

    // Wait for bouncing animation
    await new Promise(resolve => setTimeout(resolve, 1200));

    // Reveal choices with animation
    choice1Element.classList.remove('bouncing');
    choice2Element.classList.remove('bouncing');

    choice1Element.textContent = match.player1Choice || '?';
    choice2Element.textContent = match.player2Choice || '?';

    choice1Element.classList.add('choice-reveal');
    choice2Element.classList.add('choice-reveal');

    // Wait for reveal animation
    await new Promise(resolve => setTimeout(resolve, 600));

    // Clean up reveal animation
    choice1Element.classList.remove('choice-reveal');
    choice2Element.classList.remove('choice-reveal');

    // Show result
    if (match.winner) {
        // Highlight winner
        if (match.winner === match.player1) {
            choice1Element.classList.add('winner');
            player1Element.classList.add('active-player');
        } else {
            choice2Element.classList.add('winner');
            player2Element.classList.add('active-player');
        }

        resultElement.textContent = `${match.winner} wins!`;
        resultElement.classList.add('show');

        // Add winner sound effect (if available)
        try {
            // Optional: Play winner sound
            const audio = new Audio('/public/sounds/winner.mp3');
            audio.volume = 0.3;
            audio.play().catch(() => {}); // Ignore if sound fails
        } catch (e) {}

    } else if (match.player1Choice && match.player2Choice && match.player1Choice === match.player2Choice) {
        // Handle tie
        matchElement.classList.add('tie-animation');
        resultElement.textContent = "It's a tie! Playing again...";
        resultElement.classList.add('show');

        setTimeout(() => {
            matchElement.classList.remove('tie-animation');
            // Reset for replay
            setTimeout(() => {
                choice1Element.classList.remove('winner');
                choice2Element.classList.remove('winner');
                player1Element.classList.remove('active-player');
                player2Element.classList.remove('active-player');
                resultElement.classList.remove('show');
            }, 1500);
        }, 500);
    }
}

// Animate final match with countdown
async function animateFinalMatch(matchElement) {
    if (!matchElement) return;

    matchElement.classList.add('final-match');

    // Show countdown
    elements.countdown.classList.add('show');
    for (let i = 3; i > 0; i--) {
        elements.countdown.textContent = i;
        elements.countdown.style.animation = 'countdownPulse 1s ease-in-out';
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    elements.countdown.textContent = "Fight!";
    await new Promise(resolve => setTimeout(resolve, 1000));
    elements.countdown.classList.remove('show');
}

// Enhanced tournament display with animations
function updateTournamentDisplay() {
    elements.tournament.innerHTML = '';

    // Create rounds
    for (let roundIndex = 0; roundIndex <= gameState.currentRound; roundIndex++) {
        const roundElement = document.createElement('div');
        roundElement.className = 'round';

        const roundPlayers = gameState.tournamentBracket[roundIndex];

        // Create matches
        for (let i = 0; i < roundPlayers.length; i += 2) {
            const player1 = roundPlayers[i];
            const player2 = roundPlayers[i + 1] || 'Odd One Out';

            const matchElement = createMatchElement(player1, player2, roundIndex);
            roundElement.appendChild(matchElement);
        }

        elements.tournament.appendChild(roundElement);
    }

    // Show final result if game ended
    if (gameState.gameEnded && gameState.winner) {
        setTimeout(() => {
            elements.finalResult.textContent = `🏆 ${gameState.winner} is the tournament champion! 🏆`;
            elements.finalResult.classList.add('show');

            // Show confetti
            confetti?.({
                particleCount: 150,
                spread: 70,
                origin: { y: 0.6 },
                colors: ['#8BBEB2', '#18314F', '#384E77', '#4CAF50']
            });

            // Additional confetti burst
            setTimeout(() => {
                confetti?.({
                    particleCount: 100,
                    spread: 120,
                    origin: { y: 0.8 }
                });
            }, 500);

        }, 1000);

        // Show new game button for host
        if (clientState.isHost) {
            setTimeout(() => {
                elements.newGameBtn.classList.remove('hidden');
            }, 2000);
        }
    }
}

// Create a match element for the tournament display
function createMatchElement(player1, player2, roundIndex) {
    const matchElement = document.createElement('div');
    matchElement.className = 'match';

    // Find match data if available
    let matchData = null;
    if (roundIndex === gameState.currentRound) {
        matchData = gameState.matches.find(m =>
            (m.player1 === player1 && m.player2 === player2) ||
            (m.player1 === player2 && m.player2 === player1)
        );

        // Add match ID for animations
        if (matchData) {
            matchElement.setAttribute('data-match-id', matchData.id);
        }
    }

    // Create player elements
    const player1Element = createPlayerElement(player1, matchData?.player1Choice || '?');
    const player2Element = createPlayerElement(player2, matchData?.player2Choice || '?');

    matchElement.appendChild(player1Element);
    matchElement.appendChild(player2Element);

    // Add result element
    const resultElement = document.createElement('div');
    resultElement.className = 'match-result';

    if (player2 === 'Odd One Out') {
        resultElement.textContent = `${player1} advances to the next round. (Odd number of players)`;
        resultElement.classList.add('show');
        matchElement.classList.add('bye-match');
        player2Element.classList.add('bye-player');
    } else if (matchData && matchData.winner) {
        resultElement.textContent = `${matchData.winner} wins!`;
        resultElement.classList.add('show');

        // Highlight winner
        if (matchData.winner === player1) {
            player1Element.querySelector('.choice').classList.add('winner');
            player1Element.classList.add('active-player');
        } else {
            player2Element.querySelector('.choice').classList.add('winner');
            player2Element.classList.add('active-player');
        }
    }

    matchElement.appendChild(resultElement);

    return matchElement;
}

// Update the current match display
function updateMatchDisplay() {
    // Find the player's current match
    clientState.currentMatch = gameState.matches.find(match =>
        (match.player1 === clientState.playerName || match.player2 === clientState.playerName) &&
        !match.completed
    );

    // Reset choice
    clientState.myChoice = null;

    // Update UI based on match status
    if (clientState.currentMatch) {
        elements.currentMatch.classList.remove('hidden');

        // Update match info
        const opponent = clientState.currentMatch.player1 === clientState.playerName
            ? clientState.currentMatch.player2
            : clientState.currentMatch.player1;

        elements.matchInfo.textContent = `You vs ${opponent}`;

        // Show choice controls
        elements.playerChoiceControls.classList.remove('hidden');
        elements.waitingMessage.classList.add('hidden');

        // Reset choice buttons
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.classList.remove('selected');
        });

        // Update turn indicator
        elements.turnIndicator.textContent = `Round ${gameState.currentRound + 1}: Make your choice!`;
    } else {
        // Check if player is eliminated
        clientState.isEliminated = !gameState.tournamentBracket[gameState.currentRound].includes(clientState.playerName);

        if (clientState.isEliminated) {
            elements.currentMatch.classList.add('hidden');
            elements.turnIndicator.textContent = `You have been eliminated from the tournament.`;
        } else if (!gameState.gameEnded) {
            elements.currentMatch.classList.add('hidden');
            elements.turnIndicator.textContent = `Round ${gameState.currentRound + 1}: Waiting for other matches to complete...`;
        } else {
            elements.currentMatch.classList.add('hidden');
            elements.turnIndicator.textContent = `Tournament completed!`;
        }
    }
}

// Update the player list
function updatePlayerList() {
    if (!client.room) return;

    const players = client.room.players;
    elements.playerList.innerHTML = '';

    players.forEach(player => {
        const playerItem = document.createElement('div');
        playerItem.className = 'player-item';

        // Create player info container (emoji + name)
        const playerInfo = document.createElement('div');
        playerInfo.className = 'player-info';

        // Add emoji
        const emojiSpan = document.createElement('span');
        emojiSpan.className = 'player-emoji';
        emojiSpan.textContent = player.emoji || '👤'; // Use default if not set
        playerInfo.appendChild(emojiSpan);

        // Add name
        const nameSpan = document.createElement('span');
        nameSpan.className = 'player-name';
        nameSpan.textContent = player.name;

        if (player.id === clientState.clientId) {
            nameSpan.textContent += ' (You)';
        }

        playerInfo.appendChild(nameSpan);

        // Add status (host)
        const statusSpan = document.createElement('span');
        if (player.isHost) {
            statusSpan.className = 'player-host';
            statusSpan.textContent = 'Host';
        }

        playerItem.appendChild(playerInfo);
        playerItem.appendChild(statusSpan);
        elements.playerList.appendChild(playerItem);
    });
}

// Show the lobby section
function showLobby() {
    elements.lobbySection.classList.remove('hidden');
    elements.roomSection.classList.add('hidden');
    elements.gameSection.classList.add('hidden');
}

// Show the room section
function showRoom() {
    elements.lobbySection.classList.add('hidden');
    elements.roomSection.classList.remove('hidden');
    elements.gameSection.classList.add('hidden');
}

// Show the game section
function showGame() {
    elements.lobbySection.classList.add('hidden');
    elements.roomSection.classList.add('hidden');
    elements.gameSection.classList.remove('hidden');
}

// Update connection status display
function updateConnectionStatus(status) {
    elements.connectionStatus.className = 'connection-status';

    switch (status) {
        case 'connected':
            elements.connectionStatus.classList.add('connection-connected');
            elements.connectionStatus.textContent = 'Connected';
            break;
        case 'disconnected':
            elements.connectionStatus.classList.add('connection-disconnected');
            elements.connectionStatus.textContent = 'Disconnected';
            break;
        case 'connecting':
            elements.connectionStatus.classList.add('connection-connecting');
            elements.connectionStatus.textContent = 'Connecting...';
            break;
    }
}

// Show a status message
function showStatusMessage(message, type = 'info') {
    elements.statusMessage.textContent = message;
    elements.statusMessage.className = 'status-message fade-in';
    elements.statusMessage.classList.add(`status-${type}`);
    elements.statusMessage.classList.remove('hidden');

    setTimeout(() => {
        elements.statusMessage.classList.add('hidden');
    }, 3000);
}

// Shuffle an array (Fisher-Yates algorithm)
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Event handlers for WebSocket events
client.on('connected', (data) => {
    clientState.clientId = data.clientId;

    // Check for pending join from lobby browser
    const pendingJoin = sessionStorage.getItem('pendingJoin');
    if (pendingJoin) {
        try {
            const joinData = JSON.parse(pendingJoin);
            // Clear the pending join data
            sessionStorage.removeItem('pendingJoin');

            // Set player name and join the room automatically
            elements.playerNameInput.value = joinData.playerName;
            clientState.playerName = joinData.playerName;
            localStorage.setItem('playerName', joinData.playerName);

            // Join the room directly
            client.joinRoom(joinData.roomId, joinData.playerName, clientState.selectedEmoji);
            return;
        } catch (error) {
            console.error('Error processing pending join:', error);
            sessionStorage.removeItem('pendingJoin');
        }
    }

    // Check if there's a room ID in the URL (fallback for direct links)
    const urlParams = new URLSearchParams(window.location.search);
    const roomId = urlParams.get('roomId') || urlParams.get('join');
    if (roomId) {
        elements.roomIdInput.value = roomId;
        showJoinRoomForm();
    }
});

client.on('room_created', (data) => {
    clientState.roomId = data.roomId;
    clientState.isHost = true;
    elements.roomIdDisplay.textContent = data.roomId;
    elements.hostControls.classList.remove('hidden');
    updatePlayerList();
    showRoom();
});

client.on('room_joined', (data) => {
    clientState.roomId = data.roomId;
    clientState.isHost = false;
    elements.roomIdDisplay.textContent = data.roomId;
    elements.hostControls.classList.add('hidden');
    updatePlayerList();
    showRoom();
});

client.on('player_joined', (data) => {
    updatePlayerList();
    showStatusMessage(`${data.player.name} joined the room`, 'info');
});

client.on('player_left', (data) => {
    updatePlayerList();
    showStatusMessage(`A player left the room`, 'warning');
});

client.on('game_started', (data) => {
    gameState = data.room.gameState;
    elements.tournament.innerHTML = '';
    elements.finalResult.textContent = '';
    elements.finalResult.classList.remove('show');
    elements.newGameBtn.classList.add('hidden');

    updateTournamentDisplay();
    updateMatchDisplay();
    showGame();
});

client.on('game_action', (data) => {
    if (data.action === 'make_choice') {
        // Update match with player's choice
        const match = gameState.matches.find(m => m.id === data.gameState.matchId);
        if (match) {
            if (match.player1 === data.gameState.playerName) {
                match.player1Choice = data.gameState.choice;
            } else if (match.player2 === data.gameState.playerName) {
                match.player2Choice = data.gameState.choice;
            }

            // Show live choice feedback
            updateLiveChoices(match);

            // Check if both players have made choices
            if (match.player1Choice && match.player2Choice) {
                // Determine winner
                const winnerIndex = determineWinner(match.player1Choice, match.player2Choice);

                if (winnerIndex === null) {
                    // Tie - animate and reset choices
                    match.player1Choice = null;
                    match.player2Choice = null;

                    // Animate tie for all players
                    const matchElement = document.querySelector(`[data-match-id="${match.id}"]`);
                    if (matchElement) {
                        animateMatchResult(matchElement, {
                            ...match,
                            player1Choice: data.gameState.choice,
                            player2Choice: data.gameState.choice // Same choice for tie
                        });
                    }

                    // If this is the player's match, reset UI
                    if (clientState.currentMatch && clientState.currentMatch.id === match.id) {
                        clientState.myChoice = null;
                        setTimeout(() => {
                            elements.playerChoiceControls.classList.remove('hidden');
                            elements.waitingMessage.classList.add('hidden');
                            document.querySelectorAll('.choice-btn').forEach(btn => {
                                btn.classList.remove('selected');
                            });
                        }, 2000); // Allow time for tie animation
                    }
                } else {
                    // Set winner
                    match.winner = winnerIndex === 0 ? match.player1 : match.player2;
                    match.completed = true;

                    // Animate match result for all players
                    const matchElement = document.querySelector(`[data-match-id="${match.id}"]`);
                    if (matchElement) {
                        // Check if this is the final match
                        if (gameState.matches.length === 1 && gameState.tournamentBracket[gameState.currentRound].length === 2) {
                            animateFinalMatch(matchElement).then(() => {
                                animateMatchResult(matchElement, match);
                            });
                        } else {
                            animateMatchResult(matchElement, match);
                        }
                    }

                    // Process match results
                    if (clientState.isHost) {
                        processMatchResults();
                    }
                }
            }

            // Update tournament display to show current choices
            updateTournamentDisplay();
        }
    } else if (data.action === 'update_tournament') {
        gameState = data.gameState;
        updateTournamentDisplay();
        updateMatchDisplay();
    } else if (data.action === 'new_tournament') {
        gameState = data.gameState;
        elements.tournament.innerHTML = '';
        elements.finalResult.textContent = '';
        elements.finalResult.classList.remove('show');
        elements.newGameBtn.classList.add('hidden');
        elements.countdown.classList.remove('show');

        updateTournamentDisplay();
        updateMatchDisplay();
    }
});

client.on('error', (data) => {
    showStatusMessage(data.message, 'error');
});

client.on('disconnected', () => {
    updateConnectionStatus('disconnected');
    showStatusMessage('Disconnected from server. Trying to reconnect...', 'error');
});

client.on('reconnected', () => {
    updateConnectionStatus('connected');
    showStatusMessage('Reconnected to server', 'success');
});

// Initialize the application when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
}
