<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiplayer Grid Quest</title>
    <link rel="stylesheet" href="/public/css/games/grid-quest.css">
    <link rel="stylesheet" href="/multiplayer/public/css/multiplayer.css">
    <script src="/public/js/confetti.js"></script>
    <style>
        /* Enhanced Lobby UI Styles */
        .lobby-container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            padding: 1rem;
        }

        .player-name-section {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4f8 100%);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 12px rgba(24, 49, 79, 0.1);
            border: 1px solid rgba(139, 190, 178, 0.2);
        }

        .input-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .input-group label {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .name-input, .room-input {
            padding: 0.75rem 1rem;
            border: 2px solid var(--input-border);
            border-radius: 12px;
            font-size: 1rem;
            font-family: 'Poppins', sans-serif;
            transition: all 0.3s ease;
            background: white;
        }

        .name-input:focus, .room-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(139, 190, 178, 0.1);
            transform: translateY(-1px);
        }

        .divider {
            text-align: center;
            margin: 2rem 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--accent-color), transparent);
        }

        .divider-text {
            background: var(--background-color);
            color: var(--secondary-color);
            padding: 0 1rem;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .action-cards {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
            margin-top: 1.5rem;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        @media (min-width: 768px) {
            .action-cards {
                grid-template-columns: 1fr 1fr;
                max-width: 800px;
                gap: 2rem;
            }
        }

        .action-card {
            background: white;
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 8px 24px rgba(24, 49, 79, 0.08);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
        }

        .action-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(24, 49, 79, 0.12);
            border-color: var(--accent-color);
        }

        .create-card:hover::before {
            background: linear-gradient(90deg, var(--success-color), var(--accent-color));
        }

        .join-card:hover::before {
            background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
        }

        .card-icon {
            font-size: 2rem;
            text-align: center;
            margin-bottom: 1rem;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .card-content h3 {
            color: var(--primary-color);
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0 0 0.5rem 0;
            text-align: center;
        }

        .card-content p {
            color: var(--secondary-color);
            font-size: 0.85rem;
            margin: 0 0 1.5rem 0;
            text-align: center;
            line-height: 1.4;
        }

        .action-button {
            width: 100%;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 12px;
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-create {
            background: linear-gradient(135deg, var(--success-color), #45a049);
            color: white;
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .btn-create:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
        }

        .btn-join {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            color: white;
            box-shadow: 0 4px 12px rgba(56, 78, 119, 0.3);
        }

        .btn-join:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(56, 78, 119, 0.4);
        }

        .join-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .join-actions {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .clear-button {
            padding: 0.5rem;
            border: 2px solid var(--accent-color);
            background: transparent;
            color: var(--accent-color);
            border-radius: 8px;
            font-family: 'Poppins', sans-serif;
            font-weight: 500;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.3rem;
        }

        .clear-button:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-1px);
        }

        .button-icon {
            font-size: 0.9rem;
        }

        /* Join Room Form Styles */
        .join-room-form {
            margin-top: 2rem;
            animation: slideDown 0.3s ease-out;
        }

        .form-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 24px rgba(24, 49, 79, 0.12);
            border: 2px solid var(--accent-color);
            max-width: 500px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
        }

        .form-card h3 {
            color: var(--primary-color);
            font-size: 1.3rem;
            font-weight: 600;
            margin: 0 0 1.5rem 0;
            text-align: center;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .action-cards {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .lobby-container {
                padding: 0.5rem;
            }

            .player-name-section {
                padding: 1rem;
                margin-bottom: 1.5rem;
            }

            .action-card {
                padding: 1rem;
            }
        }

        @media (max-width: 480px) {
            .card-icon {
                font-size: 1.5rem;
            }

            .card-content h3 {
                font-size: 1rem;
            }

            .card-content p {
                font-size: 0.8rem;
            }

            .action-button {
                padding: 0.6rem 0.8rem;
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Multiplayer Grid Quest</h1>
        <div id="description">Find players on the grid. If you find yourself, you're selected. If you find someone else, they win!</div>

        <!-- Connection Status -->
        <div id="connectionStatus" class="connection-status connection-disconnected">Disconnected</div>

        <!-- Lobby Section -->
        <div id="lobbySection" class="multiplayer-section">
            <div class="lobby-container">
                <!-- Player Name Section (shared) -->
                <div class="player-name-section">
                    <div class="input-group">
                        <label for="playerName">👤 Your Name</label>
                        <input type="text" id="playerName" placeholder="Enter your name" class="name-input">
                    </div>
                    <div class="emoji-selector-container">
                        <label class="emoji-selector-label">Choose your emoji:</label>
                        <div id="emojiSelector" class="emoji-selector">
                            <!-- Emojis will be added here via JavaScript -->
                        </div>
                    </div>
                </div>

                <div class="divider">
                    <span class="divider-text">Choose an option</span>
                </div>

                <!-- Action Cards Container -->
                <div class="action-cards">
                    <!-- Create Room Card -->
                    <div class="action-card create-card">
                        <div class="card-icon">🎮</div>
                        <div class="card-content">
                            <h3>Create New Room</h3>
                            <p>Start a new quest and invite friends to join</p>
                            <button id="createRoomBtn" class="action-button btn-create">
                                <span class="button-icon">➕</span>
                                Create Room
                            </button>
                        </div>
                    </div>

                    <!-- Join Room Card -->
                    <div class="action-card join-card">
                        <div class="card-icon">🚪</div>
                        <div class="card-content">
                            <h3>Join Existing Room</h3>
                            <p>Enter a room ID to join an ongoing quest</p>
                            <button id="joinRoomBtn" class="action-button btn-join">
                                <span class="button-icon">🔗</span>
                                Join Room
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Join Room Form (Hidden by default) -->
                <div id="joinRoomForm" class="join-room-form hidden">
                    <div class="form-card">
                        <h3>Join Quest Room</h3>
                        <div class="join-form">
                            <div class="input-group">
                                <label for="roomIdInput">Room ID:</label>
                                <input type="text" id="roomIdInput" placeholder="Enter room ID" class="room-input" style="text-transform: uppercase;">
                            </div>
                            <div class="emoji-selector-container">
                                <label class="emoji-selector-label">Choose your emoji:</label>
                                <div id="joinEmojiSelector" class="emoji-selector">
                                    <!-- Emojis will be added here via JavaScript -->
                                </div>
                            </div>
                            <div class="join-actions">
                                <button id="confirmJoinBtn" class="action-button btn-join">
                                    <span class="button-icon">✅</span>
                                    Join Quest
                                </button>
                                <button id="cancelJoinBtn" class="clear-button">
                                    <span class="button-icon">❌</span>
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Room Section -->
        <div id="roomSection" class="multiplayer-section hidden">
            <div class="room-info">
                <div>Room: <span id="roomId" class="room-id">ABCDEF</span></div>
                <div id="copyLink" class="copy-link">Copy Link</div>
            </div>

            <div id="playerList" class="player-list">
                <!-- Players will be listed here -->
            </div>

            <div id="hostControls" class="multiplayer-controls hidden">
                <button id="startGameBtn" class="btn-success">Start Game</button>
            </div>

            <div class="multiplayer-controls">
                <button id="leaveRoomBtn" class="btn-danger">Leave Room</button>
            </div>
        </div>

        <!-- Game Section -->
        <div id="gameSection" class="multiplayer-section hidden">
            <div id="turnIndicator" class="turn-indicator">Waiting for game to start...</div>
            <div id="teamList"></div>
            <div id="grid" class="grid"></div>
            <div id="message"></div>
            <button id="newGameBtn" class="hidden">New Game</button>
        </div>

        <!-- Status Message -->
        <div id="statusMessage" class="status-message hidden"></div>
    </div>

    <script src="/multiplayer/public/js/multiplayer-client.js"></script>
    <script src="/multiplayer/games/grid-quest/grid-quest-multiplayer.js"></script>
</body>
</html>
