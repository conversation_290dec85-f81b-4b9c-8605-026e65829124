/*!
 * Person Picker Welcome Page (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

:root {
    --primary-color: #18314F;
    --secondary-color: #384E77;
    --accent-color: #8BBEB2;
    --text-color: #0D0630;
    --background-color: #FFFFFF;
    --card-background: #FFFFFF;
    --card-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    --button-hover: #6AADA0;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.welcome-container {
    max-width: 1200px;
    width: 95%;
    margin: 2rem auto;
    background-color: var(--background-color);
    border-radius: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    padding: 2rem;
}

.welcome-header {
    text-align: center;
    margin-bottom: 2rem;
}

.welcome-header h1 {
    color: var(--primary-color);
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

.subheading {
    color: var(--secondary-color);
    font-size: 1.2rem;
    margin-top: 0;
}

.welcome-intro {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.2rem;
    color: var(--text-color);
}

.mode-selection {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
}

.mode-card {
    background-color: var(--card-background);
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    padding: 2rem;
    width: 100%;
    max-width: 400px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.mode-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.mode-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.mode-card h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.mode-card p {
    color: var(--text-color);
    margin-bottom: 1.5rem;
}

.mode-card ul {
    text-align: left;
    margin-bottom: 2rem;
    padding-left: 1.5rem;
}

.mode-card li {
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.mode-button {
    display: inline-block;
    background-color: var(--accent-color);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.mode-button:hover {
    background-color: var(--button-hover);
}

.welcome-features {
    margin-top: 3rem;
    text-align: center;
}

.welcome-features h2 {
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature {
    background-color: var(--card-background);
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    transition: transform 0.3s ease;
}

.feature:hover {
    transform: translateY(-5px);
}

.feature h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature p {
    color: var(--text-color);
}

footer {
    margin-top: 3rem;
    text-align: center;
    color: var(--text-color);
    font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .mode-selection {
        flex-direction: column;
        align-items: center;
    }
    
    .mode-card {
        max-width: 100%;
    }
    
    .welcome-header h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .welcome-container {
        padding: 1.5rem;
    }
    
    .welcome-header h1 {
        font-size: 2rem;
    }
    
    .subheading {
        font-size: 1rem;
    }
}
