<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="title">Random Person Picker</title>
    
    <!-- Cache Control -->
    <meta http-equiv="Cache-Control" content="public, max-age=31536000">
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Person Picker is the ultimate random name selector for fair decisions. Perfect for team tasks, classroom activities, and more. Try our fun selection games now!">
    <meta name="keywords" content="random name picker, name selector, random picker, team selection, classroom tool">
    <meta name="author" content="Person Picker">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://personpicker.com/">
    <meta property="og:title" content="Random Person Picker - Fair Selection Made Fun">
    <meta property="og:description" content="The ultimate random name selector for fair decisions. Perfect for team tasks, classroom activities, and more.">
    <meta property="og:image" content="https://personpicker.com/android-chrome-512x512.png">
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://personpicker.com/">
    <meta name="twitter:title" content="Random Person Picker - Fair Selection Made Fun">
    <meta name="twitter:description" content="The ultimate random name selector for fair decisions. Perfect for team tasks, classroom activities, and more.">
    <meta name="twitter:image" content="https://personpicker.com/android-chrome-512x512.png">
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="/public/css/styles.css">
    <link rel="stylesheet" href="/public/css/multiplayer-hub.css">
    
    <style>
        /* Custom styles to make single player game icons match multiplayer card layout */
        .game-grid .gameIcon {
            background-color: var(--card-background);
            border-radius: 15px;
            box-shadow: var(--card-shadow);
            padding: 1.5rem;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
            border: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 120px;
            font-size: 2.5rem;
            cursor: pointer;
        }
        
        .game-grid .gameIcon:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        
        .game-grid .gameIcon::after {
            content: attr(aria-label);
            font-size: 0.9rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-top: 0.5rem;
            text-align: center;
        }
        
        /* Override the default game icons grid to use the card layout */
        #gameIcons.game-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
        
        /* Responsive adjustments for game cards */
        @media (max-width: 768px) {
            #gameIcons.game-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 1rem;
            }
            
            .game-grid .gameIcon {
                padding: 1rem;
                min-height: 100px;
                font-size: 2rem;
            }
        }
        
        @media (max-width: 480px) {
            #gameIcons.game-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "Random Person Picker",
      "description": "Person Picker is the ultimate random name selector for fair decisions. Perfect for team tasks, classroom activities, and more.",
      "url": "https://personpicker.com",
      "applicationCategory": "UtilityApplication",
      "operatingSystem": "Any",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "featureList": [
        "Random name selection",
        "Team task assignment",
        "Classroom activities",
        "Fair decision making",
        "Multiple selection games"
      ]
    }
    </script>
    
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-CXC0589Q5T"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-CXC0589Q5T');
    </script>
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="/public/js/app.js" as="script" crossorigin="anonymous">
    <link rel="preload" href="/public/js/confetti.js" as="script" crossorigin="anonymous">
    
    <script src="/public/js/confetti.js" defer crossorigin="anonymous"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>Random Person Picker</h1>
            <p class="subheading">The best random person picker for fair decisions</p>
            <nav>
                <a href="/welcome.html" class="nav-button">Back to Home</a>
                <a href="/multiplayer.html" class="nav-button">Switch to Multiplayer</a>
            </nav>
        </header>

        <div id="intro" class="section" style="text-align: center; margin-bottom: 2rem;">
            <p>Random Person Picker is the ultimate tool for making fair selections a breeze for any situation. Whether you're assigning tasks, randomly picking a winner, choosing presenters, or making group decisions, our random selector games ensure everyone gets an equal chance.</p>
        </div>

        <div class="game-selection">
            <div id="name-input" style="background-color: var(--card-background); border-radius: 15px; box-shadow: var(--card-shadow); padding: 1.5rem; margin-bottom: 2rem;">
                <h2>Enter Names</h2>
                <div id="nameInputs"></div>
                <button id="addNameBtn">Add Name</button>
                <button id="launchButton">Launch Random Selector</button>
            </div>

            <h2>Or Choose Your Game</h2>
            <div id="gameIcons" class="game-grid" role="group" aria-label="Available random selection games"></div>
        </div>

        <div class="how-to-play">
            <h2>How to Use Random Person Picker</h2>
            
            <div class="steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h3>Enter Names</h3>
                    <p>Add the names of people you want to include in the random selection.</p>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <h3>Choose a Game</h3>
                    <p>Select one of our fun selection games or use the basic launcher.</p>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <h3>Start Selection</h3>
                    <p>Click the launch button or game icon to begin the random selection process.</p>
                </div>
                
                <div class="step">
                    <div class="step-number">4</div>
                    <h3>See Results</h3>
                    <p>Watch as the system fairly selects a person at random from your list.</p>
                </div>
            </div>
        </div>

        <div id="use-cases">
            <h2 class="perfect-for">Perfect For...</h2>
            <div class="use-cases-grid">
                <div class="use-case">
                    <h3>Team Collaboration</h3>
                    <p>Assign roles or tasks fairly to team members, enhancing engagement.</p>
                </div>
                <div class="use-case">
                    <h3>Decision Making</h3>
                    <p>Resolve conflicts or make unbiased selections effortlessly.</p>
                </div>
                <div class="use-case">
                    <h3>Classroom Activities</h3>
                    <p>Select students for presentations or group assignments fairly.</p>
                </div>
                <div class="use-case">
                    <h3>Name Selection</h3>
                    <p>Choose a name from any list with ease and fairness.</p>
                </div>
                <div class="use-case">
                    <h3>Destination Picker</h3>
                    <p>Pick a restaurant, bar, or holiday location for your next outing.</p>
                </div>
                <div class="use-case">
                    <h3>Winner Selection</h3>
                    <p>Fairly select prize winners for competitions or giveaways.</p>
                </div>
            </div>
        </div>
        
        <footer>
            <p>© 2025 Person Picker - Your Trusted Random Name Selector. All rights reserved.</p>
        </footer>
    </div>

    <button id="backButton" style="display: none;">Back to Selector</button>
    <iframe id="gameFrame" style="display: none;"></iframe>

    <script src="/public/js/app.js" type="module"></script>
</body>
</html>